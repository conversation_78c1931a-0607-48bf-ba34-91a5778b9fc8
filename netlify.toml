[images]
  remote_images = ["https://source.unsplash.com/.*", "https://images.unsplash.com/.*", "https://ext.same-assets.com/.*", "https://ugc.same-assets.com/.*"]

[build]
  command = "bun run build"
  publish = ".next"

[build.environment]
  NETLIFY_NEXT_PLUGIN_SKIP = "true"

[[plugins]]
  package = "@netlify/plugin-nextjs"

# 防止URL添加尾部斜杠的重定向规则
[[redirects]]
  from = "/articles"
  to = "/articles.html"
  status = 200

[[redirects]]
  from = "/articles/detail"
  to = "/articles/detail.html"
  status = 200