import { get } from '@/lib';

/**
 * 文章类别接口返回类型
 */
export interface ArticleCategory {
  id: number;
  projectId: number;
  name: string;
  sort: number;
  isShow: number;
  isDelete: number;
  createTime: number;
  updateTime: number;
  deleteTime: number;
}

/**
 * 文章列表项接口返回类型
 */
export interface ArticleItem {
  id: number;
  projectId: number;
  language: string;
  title: string;
  content: string;
  image: string;
  author: string;
  collect: number;
  sort: number;
  isShow: number;
  createTime: string;
  updateTime: string;
  categoryIds: string | null;
  categoryList: any[];
  isVip: number;
  questionType: string;
  isCollect: boolean;
}

/**
 * 文章详情接口返回类型
 */
export interface ArticleDetail {
  id: number;
  projectId: number;
  language: string;
  title: string;
  intro: string;
  image: string;
  content: string;
  author: string;
  collect: number;
  sort: number;
  isShow: number;
  categories: {
    id: number;
    name: string;
  }[];
  isVip: number;
  questionType: string;
  isCollect: boolean;
}

/**
 * 分页数据结构
 */
export interface Pagination<T> {
  lists: T[];
  count: number;
  pageNo: number;
  pageSize: number;
}

/**
 * 获取文章类目接口
 * @returns 文章类目列表
 */
export const getArticleCategories = async (): Promise<ArticleCategory[]> => {
  return get('/article/category/list/h5');
};

/**
 * 获取文章分页列表接口
 * @param params 查询参数
 * @returns 文章分页列表
 */
export const getArticleList = async (params: {
  categoryId?: number;
  sortType?: number; // 1: 最新, 2: 最热, 3: 推荐
  pageNo?: number;
  pageSize?: number;
}): Promise<Pagination<ArticleItem>> => {
  return get('/article/list/h5', params);
};

/**
 * 获取文章详情接口
 * @param id 文章ID
 * @returns 文章详情
 */
export const getArticleDetail = async (id: number): Promise<ArticleDetail> => {
  return get('/article/detail/h5', { id });
};

/**
 * 获取文章相关推荐接口
 * @param params 查询参数
 * @returns 相关文章列表
 */
export const getRelatedArticles = async (params: {
  categoryId: number;
  articleId: number;
  topN?: number;
}): Promise<ArticleItem[]> => {
  return get('/article/related/h5', params);
};
