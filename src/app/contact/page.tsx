import Layout from "@/components/layout/Layout";
import Link from "next/link";

export default function ContactPage() {
  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-6 py-12">
        <h1 className="text-4xl md:text-5xl font-bold uppercase tracking-wider text-center mb-12">
          Contact Us
        </h1>

        <div className="space-y-6 text-center">
          <p className="text-lg font-medium">
            Customer Support Hours: Monday - Friday 8:00am to 5:00pm EST.
          </p>

          <p className="text-lg">
            For assistance navigating Lumina, please contact us during our normal business hours at{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-black font-semibold hover:underline"
            >
              <EMAIL>
            </a>
            . For inquiries submitted after hours, our team will respond as soon as possible.
          </p>

          <p className="text-lg">
            For immediate assistance, we invite you to visit our{" "}
            <Link
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              className="text-black font-semibold hover:underline"
            >
              Help Center
            </Link>
            , which contains FAQs to many of the features found in Lumina.
          </p>

          <p className="text-lg">
            <Link
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              className="text-black font-semibold hover:underline"
            >
              Video tutorials
            </Link>
            {" "}on some of the most popular features in Lumina can be found on our
            YouTube channel. Please subscribe to stay informed about app updates and news!
          </p>
        </div>
      </div>
    </Layout>
  );
}
