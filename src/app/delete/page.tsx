import Layout from '@/components/layout/Layout'
import React from 'react'

export default function page() {
    return (
        <Layout>
            <div className="max-w-4xl mx-auto px-6 py-12">
                <h1 className="text-4xl md:text-5xl font-bold uppercase tracking-wider text-center mb-12">
                    Delete Account
                </h1>

                <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-8">
                    <div className="flex">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <p className="text-sm text-red-700">
                                Warning: This action cannot be undone. All your data will be permanently deleted.
                            </p>
                        </div>
                    </div>
                </div>

                <div className="bg-white shadow rounded-lg p-6 mb-8">
                    <h2 className="text-xl font-semibold mb-4">Before you delete your account</h2>
                    <ul className="list-disc pl-6 space-y-2 text-gray-700">
                        <li>All your personal data will be permanently removed</li>
                        <li>Your subscription will be cancelled</li>
                        <li>You will lose access to all your readings and insights</li>
                        <li>This action cannot be reversed</li>
                    </ul>
                </div>

                <div className="bg-white shadow rounded-lg p-6">
                    <h2 className="text-xl font-semibold mb-4">Confirm Account Deletion</h2>
                    <p className="text-gray-700 mb-4">To delete your account, please enter your email address below:</p>

                    <form className="space-y-4">
                        <div>
                            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                                Email Address
                            </label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500"
                                required
                            />
                        </div>

                        <div className="flex items-center">
                            <input
                                id="confirm-delete"
                                name="confirm-delete"
                                type="checkbox"
                                className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                                required
                            />
                            <label htmlFor="confirm-delete" className="ml-2 block text-sm text-gray-700">
                                I understand that this action cannot be undone and all my data will be permanently deleted.
                            </label>
                        </div>

                        <div className="pt-4">
                            <button
                                type="submit"
                                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            >
                                Delete My Account
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </Layout>
    )
}
