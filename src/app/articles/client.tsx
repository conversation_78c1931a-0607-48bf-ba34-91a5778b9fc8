'use client'

import { useEffect, useState, useRef, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import Layout from '@/components/layout/Layout'
import Image from 'next/image'
import Link from 'next/link'
import { getArticleCategories, getArticleList, ArticleCategory, ArticleItem, Pagination } from '@/api/article'
import { toast } from 'sonner'
import { getImageUrl } from '@/lib/constants'

export default function ArticlesClient() {
  const searchParams = useSearchParams()
  const categoryParam = searchParams.get('category')

  // 状态管理
  const [categories, setCategories] = useState<ArticleCategory[]>([])
  const [selectedCategory, setSelectedCategory] = useState<number | null>(categoryParam ? Number(categoryParam) : null)
  const [articles, setArticles] = useState<ArticleItem[]>([])
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [pageNo, setPageNo] = useState(1)
  const [initialLoading, setInitialLoading] = useState(true)

  const observer = useRef<IntersectionObserver | null>(null)

  // 获取文章列表
  const fetchArticles = useCallback(async (categoryId: number, page: number) => {
    if (page === 1) {
      setInitialLoading(true)
    } else {
      setLoading(true)
    }

    try {
      const data = await getArticleList({
        categoryId,
        pageNo: page,
        pageSize: 10,
        sortType: 1 // 最新
      })

      if (page === 1) {
        setArticles(data.lists)
      } else {
        setArticles(prev => [...prev, ...data.lists])
      }

      setHasMore(data.lists.length > 0 && data.count > page * 10)
      setPageNo(page)
    } catch (error) {
      console.error('Failed to fetch articles:', error)
      toast.error('Failed to load articles')

      // 如果API调用失败，使用一些默认文章
      if (page === 1) {
        const defaultArticles = [
          {
            id: 45,
            projectId: 8,
            language: "en",
            title: "What's Crystal Singing Bowl?",
            image: "appgalaxy/image/20250507/1746605856977267.png",
            author: "Jessica Miller",
            collect: 97,
            sort: 0,
            isShow: 1,
            createTime: "2025-05-07 16:21:52",
            updateTime: "2025-05-07 17:26:42",
            categoryIds: null,
            categoryList: [],
            isVip: 1,
            questionType: "4",
            isCollect: false
          },
          {
            id: 43,
            projectId: 8,
            language: "en",
            title: "Tips for Easing Anxiety",
            image: "appgalaxy/image/20250507/1746608821944726.png",
            author: "Jonas Brown",
            collect: 98,
            sort: 0,
            isShow: 1,
            createTime: "2025-05-07 16:08:37",
            updateTime: "2025-05-07 17:24:12",
            categoryIds: null,
            categoryList: [],
            isVip: 0,
            questionType: "5",
            isCollect: false
          }
        ]
        setArticles(defaultArticles)
        setHasMore(false)
      }
    } finally {
      setLoading(false)
      setInitialLoading(false)
    }
  }, [])

  // 加载更多文章
  const loadMoreArticles = useCallback(() => {
    if (selectedCategory !== null && !loading && hasMore) {
      fetchArticles(selectedCategory, pageNo + 1)
    }
  }, [selectedCategory, loading, hasMore, pageNo, fetchArticles])

  // 设置无限滚动的引用
  const lastArticleRef = useCallback((node: HTMLDivElement) => {
    if (loading) return
    if (observer.current) observer.current.disconnect()

    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        loadMoreArticles()
      }
    })

    if (node) observer.current.observe(node)
  }, [loading, hasMore, loadMoreArticles])

  // 加载文章类别 - 只在组件挂载时执行一次
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const data = await getArticleCategories()
        setCategories(data)

        // 只在初始加载时设置类别
        if (data.length > 0) {
          // 如果URL中有类别参数，使用它；否则使用第一个类别
          if (categoryParam && data.some(cat => cat.id === Number(categoryParam))) {
            setSelectedCategory(Number(categoryParam))
          } else {
            setSelectedCategory(data[0].id)
            // 更新URL查询参数
            const url = new URL(window.location.href)
            url.searchParams.set('category', data[0].id.toString())
            window.history.pushState({}, '', url.toString())
          }
        }
      } catch (error) {
        console.error('Failed to fetch categories:', error)
        toast.error('Failed to load article categories')

        // 如果API调用失败，使用一些默认类别
        const defaultCategories = [
          { id: 4, projectId: 8, name: "Top Picks", sort: 2, isShow: 1, isDelete: 0, createTime: 1745909958, updateTime: 1746516932, deleteTime: 0 },
          { id: 3, projectId: 8, name: "Latest Reads", sort: 1, isShow: 1, isDelete: 0, createTime: 1745909958, updateTime: 1746516932, deleteTime: 0 }
        ]
        setCategories(defaultCategories)

        // 设置默认类别
        if (categoryParam && defaultCategories.some(cat => cat.id === Number(categoryParam))) {
          setSelectedCategory(Number(categoryParam))
        } else {
          setSelectedCategory(defaultCategories[0].id)
          // 更新URL查询参数
          const url = new URL(window.location.href)
          url.searchParams.set('category', defaultCategories[0].id.toString())
          window.history.pushState({}, '', url.toString())
        }
      }
    }

    fetchCategories()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // 加载文章列表
  useEffect(() => {
    if (selectedCategory !== null) {
      setArticles([])
      setPageNo(1)
      setHasMore(true)
      fetchArticles(selectedCategory, 1)
    }
  }, [selectedCategory, fetchArticles])

  // 处理URL中的尾部斜杠问题
  useEffect(() => {
    // 仅在客户端执行
    if (typeof window !== 'undefined') {
      // 如果URL路径以斜杠结尾且有查询参数，则移除尾部斜杠
      if (window.location.pathname.endsWith('/') && window.location.search) {
        const newPath = window.location.pathname.slice(0, -1) + window.location.search;
        window.history.replaceState({}, '', newPath);
      }
    }
  }, [])



  // 切换类别
  const handleCategoryChange = (categoryId: number) => {
    // 如果已经是当前选中的类别，不做任何操作
    if (selectedCategory === categoryId) return

    // 更新选中的类别
    setSelectedCategory(categoryId)

    // 更新URL查询参数
    const url = new URL(window.location.href)
    url.searchParams.set('category', categoryId.toString())
    window.history.pushState({}, '', url.toString())
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8 text-center">Explore</h1>

        {/* 类别选择器 */}
        <div className="mb-8 overflow-x-auto">
          <div className="flex space-x-4 pb-2 min-w-max justify-center">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => handleCategoryChange(category.id)}
                className={`px-5 py-2 rounded-full transition-colors text-sm font-medium ${
                  selectedCategory === category.id
                    ? 'bg-black text-white'
                    : 'bg-gray-100 hover:bg-gray-200'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* 文章列表 */}
        {initialLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {articles.map((article, index) => {
                const isLastArticle = index === articles.length - 1

                return (
                  <div
                    key={article.id}
                    ref={isLastArticle ? lastArticleRef : null}
                    className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow flex flex-col h-full"
                  >
                    <Link href={`/articles/detail?id=${article.id}`}>
                      <div className="relative aspect-square w-full">
                        <Image
                          src={getImageUrl(article.image)}
                          alt={article.title}
                          fill
                          className="object-cover"
                        />
                        {article.isVip === 1 && (
                          <div className="absolute top-2 right-2 bg-yellow-500 text-white px-2 py-1 text-xs rounded">
                            VIP
                          </div>
                        )}
                      </div>
                      <div className="p-4 flex-1 flex flex-col">
                        <h2 className="text-lg font-semibold mb-2 line-clamp-2">{article.title}</h2>

                        {/* 文章简介 - 从content中提取，最多显示三行 */}
                        {article.content && (
                          <div
                            className="text-sm text-gray-600 mb-3 line-clamp-3 overflow-hidden"
                            dangerouslySetInnerHTML={{
                              __html: article.content.replace(/<[^>]*>/g, '') // 移除HTML标签
                            }}
                          />
                        )}

                        <div className="flex justify-between items-center text-sm text-gray-600 mt-auto">
                          <span className="truncate mr-2">{article.author}</span>
                          <div className="flex items-center flex-shrink-0">
                            <img
                              src="/images/bookmark-icon.svg"
                              alt="Collect"
                              className="w-4 h-4 mr-1"
                            />
                            <span>{article.collect}</span>
                          </div>
                        </div>
                      </div>
                    </Link>
                  </div>
                )
              })}
            </div>

            {/* 加载更多指示器 */}
            {loading && (
              <div className="flex justify-center mt-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black"></div>
              </div>
            )}

            {!hasMore && articles.length > 0 && (
              <div className="text-center mt-8 text-gray-500">
                No more articles to load
              </div>
            )}

            {articles.length === 0 && !initialLoading && (
              <div className="text-center py-12 text-gray-500">
                No articles found in this category
              </div>
            )}
          </>
        )}
      </div>
    </Layout>
  )
}
