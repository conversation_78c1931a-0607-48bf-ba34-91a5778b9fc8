'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import Layout from '@/components/layout/Layout'
import Image from 'next/image'
import Link from 'next/link'
import { getArticleDetail, getRelatedArticles, ArticleDetail, ArticleItem } from '@/api/article'
import { toast } from 'sonner'
import { getImageUrl } from '@/lib/constants'

export default function ArticleDetailClient() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const id = searchParams.get('id')
  const articleId = id ? Number(id) : null

  const [article, setArticle] = useState<ArticleDetail | null>(null)
  const [relatedArticles, setRelatedArticles] = useState<ArticleItem[]>([])
  const [loading, setLoading] = useState(true)

  // 处理URL中的尾部斜杠问题
  useEffect(() => {
    // 仅在客户端执行
    if (typeof window !== 'undefined') {
      // 如果URL路径以斜杠结尾且有查询参数，则移除尾部斜杠
      if (window.location.pathname.endsWith('/') && window.location.search) {
        const newPath = window.location.pathname.slice(0, -1) + window.location.search;
        window.history.replaceState({}, '', newPath);
      }
    }
  }, [])

  useEffect(() => {
    // 如果没有文章ID，重定向到文章列表页面
    if (!articleId) {
      router.push('/articles')
      return
    }

    const fetchArticleDetail = async () => {
      setLoading(true)
      try {
        const data = await getArticleDetail(articleId)
        setArticle(data)

        // 获取相关文章
        if (data.categories && data.categories.length > 0) {
          const categoryId = data.categories[0].id
          try {
            const related = await getRelatedArticles({ categoryId, articleId, topN: 4 })
            setRelatedArticles(related)
          } catch (relatedError) {
            console.error('Failed to fetch related articles:', relatedError)
            // 不显示错误提示，只是不显示相关文章
          }
        }
      } catch (error) {
        console.error('Failed to fetch article details:', error)
        toast.error('Failed to load article details')
      } finally {
        setLoading(false)
      }
    }

    fetchArticleDetail()
  }, [articleId, router])

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
        </div>
      </Layout>
    )
  }

  if (!article) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 py-12 text-center">
          <h1 className="text-2xl font-bold mb-4">Article not found</h1>
          <p className="mb-6">The article you're looking for doesn't exist or has been removed.</p>
          <Link href="/articles" className="inline-block bg-black text-white px-6 py-2 rounded">
            Back to Articles
          </Link>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 文章头部 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">{article.title}</h1>
          <div className="flex justify-between items-center text-gray-600 mb-6">
            <div className="flex items-center">
              <span className="mr-4">{article.author}</span>
              {article.isVip === 1 && (
                <span className="bg-yellow-500 text-white px-2 py-1 text-xs rounded">VIP</span>
              )}
            </div>
            <div className="flex items-center">
              <img
                src="/images/bookmark-icon.svg"
                alt="Collect"
                className="w-5 h-5 mr-1"
              />
              <span>{article.collect}</span>
            </div>
          </div>

          {/* 文章封面图 */}
          {article.image && (
            <div className="relative aspect-square w-full max-w-md mx-auto mb-6">
              <Image
                src={getImageUrl(article.image)}
                alt={article.title}
                fill
                className="object-cover rounded-lg"
              />
            </div>
          )}

          {/* 文章分类 */}
          {article.categories && article.categories.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-6">
              {article.categories.map(category => (
                <Link
                  key={category.id}
                  href={`/articles?category=${category.id}`}
                  className="bg-gray-100 px-3 py-1 rounded-full text-sm hover:bg-gray-200"
                >
                  {category.name}
                </Link>
              ))}
            </div>
          )}
        </div>

        {/* 文章内容 */}
        <div
          className="prose max-w-none mb-12"
          dangerouslySetInnerHTML={{ __html: article.content }}
        />

        {/* 相关文章 */}
        {relatedArticles.length > 0 && (
          <div className="border-t pt-8">
            <h2 className="text-2xl font-bold mb-6">Related Articles</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {relatedArticles.map(relatedArticle => (
                <Link
                  key={relatedArticle.id}
                  href={`/articles/detail?id=${relatedArticle.id}`}
                  className="flex gap-4 group"
                >
                  <div className="relative aspect-square w-20 h-20 flex-shrink-0">
                    <Image
                      src={getImageUrl(relatedArticle.image)}
                      alt={relatedArticle.title}
                      fill
                      className="object-cover rounded"
                    />
                  </div>
                  <div>
                    <h3 className="font-medium group-hover:underline line-clamp-2">{relatedArticle.title}</h3>
                    <p className="text-sm text-gray-600 mt-1">{relatedArticle.author}</p>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </Layout>
  )
}
