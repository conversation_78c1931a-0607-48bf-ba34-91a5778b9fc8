import Layout from "@/components/layout/Layout";
import Image from "next/image";

export default function AboutPage() {
  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-6 py-12">
        <h1 className="text-4xl md:text-5xl font-bold uppercase tracking-wider text-center mb-6">
          Lumina Life
        </h1>

        <div className="space-y-8 text-center">
          <div className="space-y-3">
            <h2 className="text-2xl font-bold">【 24/7 AI SoulGuide】</h2>
            <p className="text-lg">
              Meet your empathetic AI companion, designed to help you navigate life's challenges in love, career, and personal growth.
              Share your thoughts freely and receive personalized, actionable advice rooted in modern psychology and holistic wellness principles.
              Whether you're facing a tough decision or seeking daily motivation, your AI guide adapts to your unique needs.
            </p>
          </div>

          <div className="space-y-3">
            <h2 className="text-2xl font-bold">【Massive healing information】</h2>
            <p className="text-lg">
              Provides rich information content so that you can learn more about self-healing and growth methods during the reading process,
              including: Energy healing, Spatial energy, Mood regulation, Social Intelligence, Career growth, etc.
              Each module provides practical skills to improve the quality of daily life.
            </p>
          </div>

          <div className="space-y-3">
            <h2 className="text-2xl font-bold">【Self-Discovery Tools】</h2>
            <p className="text-lg">
              Constellation-Inspired Reflection: Use constellation archetypes as metaphors for self-awareness, not fortune-telling.
              <br />
              Birth Chart Exploration: Discover personality traits and potential through a symbolic 12-house analysis (birth details optional).
            </p>
          </div>

          <div className="space-y-3">
            <h2 className="text-2xl font-bold">Why Lumina Life Stands Out</h2>
            <p className="text-lg">
              ✅ Always Evolving: AI learns from your feedback to refine guidance.
              <br />
              ✅ Balance-Driven: Combines logic and empathy—no mystical jargon, just grounded support.
              <br />
              ✅ Flexible Use: Engage deeply or check in for quick pep talks. Perfect for busy lifestyles.
            </p>
          </div>

          <div className="flex justify-center mt-8">
            <Image
              src="/images/logo.png"
              alt="Lumina Logo"
              width={120}
              height={120}
              className="object-contain"
            />
          </div>
        </div>
      </div>
    </Layout>
  );
}
