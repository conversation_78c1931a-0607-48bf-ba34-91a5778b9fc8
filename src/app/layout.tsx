import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON>, <PERSON> } from "next/font/google";
import "@/app/globals.css";
import { Toaster } from 'sonner';

const inter = Inter({ subsets: ["latin"], variable: "--font-sans" });
const os<PERSON> = <PERSON>({ subsets: ["latin"], variable: "--font-heading" });

export const metadata: Metadata = {
    title: "Lumina - Go Deeper",
    description: "<PERSON><PERSON> offers a detailed guide to your personality, helping you better understand yourself and connect with others on a deeper level.",
    icons: {
        icon: "/images/logo.png",
    },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
    return (
        <html lang="en" className={`${inter.variable} ${oswald.variable}`}>
            <body className="antialiased bg-white min-h-screen">
                <Toaster position="top-center" />
                {children}
            </body>
        </html>
    );
}
