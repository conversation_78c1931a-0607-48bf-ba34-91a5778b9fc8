"use client";

import Image from "next/image";
import Link from "next/link";

const HeroSection = () => {
  return (
    <section className="py-16 flex flex-col items-center text-center">
      <h1 className="text-5xl md:text-6xl font-bold uppercase tracking-wider mb-6">
        Lumina
      </h1>
      <h2 className="text-2xl md:text-3xl uppercase tracking-wide mb-8">
      Grow in Light, Rise with Clarity.
      </h2>

      {/* App Store Style Image Showcase */}
      <div className="w-full max-w-4xl mb-10">
        {/* Mobile: Show images in a scrollable container */}
        <div className="md:hidden flex overflow-x-auto pb-4 gap-3 snap-x snap-mandatory" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
          <style jsx>{`
            div::-webkit-scrollbar {
              display: none;
            }
          `}</style>
          {[1, 2, 3, 4, 5].map((num) => (
            <div
              key={num}
              className="relative flex-shrink-0 rounded-xl overflow-hidden shadow-md snap-center mx-2 first:ml-4 last:mr-4"
              style={{
                width: '140px',
                height: '304px', // 保持原始比例 645:1398 ≈ 140:304
              }}
            >
              <Image
                src={`/images/img${num}.png`}
                alt={`App Screenshot ${num}`}
                width={645}
                height={1398}
                style={{ objectFit: 'contain' }}
                className="w-full h-full"
                priority={num <= 3}
              />
            </div>
          ))}
        </div>

        {/* Desktop: Show all 5 images in a grid */}
        <div className="hidden md:flex md:justify-center gap-4 px-4">
          {[1, 2, 3, 4, 5].map((num) => (
            <div
              key={num}
              className="relative rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow"
              style={{
                width: '184px',
                height: '400px', // 保持原始比例 645:1398 ≈ 184:400
              }}
            >
              <Image
                src={`/images/img${num}.png`}
                alt={`App Screenshot ${num}`}
                width={645}
                height={1398}
                style={{ objectFit: 'contain' }}
                className="w-full h-full"
                priority={num <= 3}
              />
            </div>
          ))}
        </div>

        {/* Indicator dots for mobile */}
        <div className="flex justify-center gap-1 mt-3 md:hidden">
          {[1, 2, 3, 4, 5].map((num) => (
            <div key={num} className="w-2 h-2 rounded-full bg-gray-300"></div>
          ))}
        </div>
      </div>

      <div className="flex flex-col sm:flex-row items-center gap-4">
        <Link
          href="https://play.google.com/store/apps/details?id=com.astro.lumina.android"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            src="/images/google-play.png"
            alt="Get it on Google Play"
            width={160}
            height={60}
            className="object-contain"
          />
        </Link>
        <Link
          href="https://apps.apple.com/us/app/lumina-life/id6744642007"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            src="/images/app-store.png"
            alt="Download on the App Store"
            width={160}
            height={60}
            className="object-contain"
          />
        </Link>
      </div>
    </section>
  );
};

export default HeroSection;
