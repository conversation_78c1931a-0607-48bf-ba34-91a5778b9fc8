import Image from "next/image";
import Link from "next/link";

const DownloadSection = () => {
  return (
    <section className="py-16 flex flex-col items-center text-center">
      <Image
        src="/images/pattern-logo-text.png"
        alt="Lumina Logo"
        width={100}
        height={20}
        className="object-contain mb-10"
      />

      <h2 className="text-2xl md:text-3xl font-bold mb-6">
        Download Lumina to get the most in-depth astrological insights today!
      </h2>

      <div className="flex flex-col sm:flex-row items-center gap-4">
        <Link
          href=""
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            src="/images/google-play.png"
            alt="Get it on Google Play"
            width={160}
            height={60}
            className="object-contain"
          />
        </Link>
        <Link
          href=""
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            src="/images/app-store.png"
            alt="Download on the App Store"
            width={160}
            height={60}
            className="object-contain"
          />
        </Link>
      </div>
    </section>
  );
};

export default DownloadSection;
