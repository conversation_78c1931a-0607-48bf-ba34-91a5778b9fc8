import Image from "next/image";

const FeaturesSection = () => {
  return (
    <section className="py-16">
      <div className="max-w-7xl mx-auto px-6">
        <h2 className="text-3xl md:text-4xl font-bold uppercase tracking-wide text-center mb-16">
          Features
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
          {/* Feature 1 */}
          <div className="flex flex-col items-center text-center">
            <Image
              src="/images/feature-pattern.png"
              alt="Your Pattern"
              width={120}
              height={120}
              className="object-contain mb-6"
            />
            <h3 className="text-2xl font-bold uppercase mb-6">Your Pattern</h3>
            <p className="text-gray-700">
              Customized, in-depth insights into your various personality traits and
              an informative breakdown of notable cycles you may be experiencing at
              any point in time - past, present and future.
            </p>
          </div>

          {/* Feature 2 */}
          <div className="flex flex-col items-center text-center">
            <Image
              src="/images/feature-bonds.png"
              alt="Bonds"
              width={120}
              height={120}
              className="object-contain mb-6"
            />
            <h3 className="text-2xl font-bold uppercase mb-6">Bonds</h3>
            <p className="text-gray-700">
              Explore your unique compatibility with friends and romantic interests.
              Gain valuable insights about your most important relationships - or
              those of your favorite public figures.
            </p>
          </div>

          {/* Feature 3 */}
          <div className="flex flex-col items-center text-center">
            <Image
              src="/images/feature-connect.png"
              alt="Connect"
              width={120}
              height={120}
              className="object-contain mb-6"
            />
            <h3 className="text-2xl font-bold uppercase mb-6">Connect</h3>
            <p className="text-gray-700">
              Go Deeper with Connect dating. Utilizing Lumina's unique
              methodology, Connect is a brand new way to make the most
              authentic & compatible connections.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
