import Link from "next/link";
import Image from "next/image";

const Header = () => {
    return (
        <header className="w-full py-4 px-6 flex justify-center items-center">
            <div className="max-w-7xl w-full flex flex-col items-center">
                <div className="w-full flex justify-center items-center">
                    <Link href="/" className="flex items-center">
                        <Image
                            src="/images/logo.png"
                            alt="Lumina Logo"
                            width={50}
                            height={50}
                            className="object-contain"
                        />
                    </Link>
                </div>
                <nav className="mt-2 flex flex-wrap justify-center items-center text-center">
                    <Link href="/about" className="px-4 py-2 uppercase text-sm tracking-wider hover:underline">
                        About Us
                    </Link>
                    <Link href="/articles" className="px-4 py-2 uppercase text-sm tracking-wider hover:underline">
                        Explore
                    </Link>
                    <Link href="/contact" className="px-4 py-2 uppercase text-sm tracking-wider hover:underline">
                        Contact Us
                    </Link>
                </nav>
            </div>
        </header>
    );
};

export default Header;
