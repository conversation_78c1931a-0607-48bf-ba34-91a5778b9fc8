import Link from "next/link";
import Image from "next/image";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

const Footer = () => {
	return (
		<footer className="w-full bg-gray-100 py-12">
			<div className="max-w-7xl mx-auto px-6">
				<div className="flex flex-wrap justify-center gap-6">
					<Link href="/terms-of-service" className="text-gray-600 hover:text-gray-900">
						Terms of Service
					</Link>
					<Link href="/privacy-policy" className="text-gray-600 hover:text-gray-900">
						Privacy Policy
					</Link>
					<Link href="/contact" className="text-gray-600 hover:text-gray-900">
						Contact & Help
					</Link>
					{/* <Link href="https://thepattern.zendesk.com/hc/en-us" className="text-gray-600 hover:text-gray-900">
						FAQ
					</Link>
					<Link href="/community-guidelines" className="text-gray-600 hover:text-gray-900">
						Community Guidelines
					</Link> */}
				</div>
				
				<div className="flex justify-center items-center gap-6">
					<Link href="https://www.instagram.com/thepattern/" target="_blank" rel="noopener noreferrer">
						<Image
							src="/images/instagram.png"
							alt="Instagram"
							width={28}
							height={28}
						/>
					</Link>
					<Link href="https://twitter.com/thepattern" target="_blank" rel="noopener noreferrer">
						<Image
							src="/images/twitter.png"
							alt="Twitter"
							width={28}
							height={28}
						/>
					</Link>
					<Link href="https://www.facebook.com/thepatternapp/" target="_blank" rel="noopener noreferrer">
						<Image
							src="/images/facebook.png"
							alt="Facebook"
							width={28}
							height={28}
						/>
					</Link>
					<Link href="https://www.tiktok.com/@thepattern" target="_blank" rel="noopener noreferrer">
						<Image
							src="/images/tiktok.png"
							alt="TikTok"
							width={28}
							height={28}
						/>
					</Link>
				</div> 
			</div>
		</footer>
	);
};

export default Footer;
