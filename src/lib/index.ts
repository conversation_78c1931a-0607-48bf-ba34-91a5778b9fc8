import Axios from 'axios';

// 根据环境设置 baseURL
const baseURL = process.env.NEXT_PUBLIC_API_URL || '/api';

const axiosInstance = Axios.create({
    baseURL,
    timeout: 60000,
    headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json; charset=utf-8',
        mode: 'cors',
        'Access-Control-Allow-Origin': '*',
        'Cache-Control': 'force-cache'
    }
});

axiosInstance.interceptors.request.use(
    async (config: any) => {
        return Promise.resolve(config);
    },
    async error => {
        return Promise.reject(error);
    }
);

axiosInstance.interceptors.response.use(async response => {
    return Promise.resolve(response);
}, async error => {
    return Promise.reject(error);
});

export const post = async (api: string, data?: any, config?: any) => {
    try {
        const response: any = await axiosInstance.post(api, data, config);
        if (response.status === 200) {
            const data = response.data
            return Promise.resolve(data.data);
        }
        return Promise.reject(response);
    } catch (error) {
        return Promise.reject(error);
    }
};

export const get = async (api: string, data?: any, config: any = {}) => {
    try {
        config.params = data;
        const response: any = await axiosInstance.get(api, config);
        if (response.status === 200) {
            const data = response.data
            return Promise.resolve(data.data);
        }
        return Promise.reject(response);
    } catch (error) {
        return Promise.reject(error);
    }
};